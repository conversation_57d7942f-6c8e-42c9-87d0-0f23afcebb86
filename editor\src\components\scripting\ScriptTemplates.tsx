/**
 * 脚本模板管理器
 * 提供预定义的脚本模板
 */
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Card,
  Button,
  Input,
  Space,
  Tag,
  Typography,
  Divider,
  message,
  Tabs,
  Rate,
  Badge,
  Tooltip,
  Select
} from 'antd';
import {
  CodeOutlined,
  ApartmentOutlined,
  SearchOutlined,
  EyeOutlined,
  DownloadOutlined,
  StarOutlined,
  StarFilled,
  ClockCircleOutlined,
  UserOutlined,
  TagOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

/**
 * 脚本模板接口
 */
interface ScriptTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: 'javascript' | 'typescript' | 'visual_script';
  tags: string[];
  content: string;
  preview?: string;
  author?: string;
  version?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  rating?: number;
  downloads?: number;
  lastUpdated?: string;
  dependencies?: string[];
  examples?: string[];
  documentation?: string;
  isFavorite?: boolean;
  isRecent?: boolean;
}

/**
 * 脚本模板属性
 */
interface ScriptTemplatesProps {
  /** 是否显示模态框 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 选择模板回调 */
  onSelectTemplate: (template: ScriptTemplate) => void;
}

/**
 * 脚本模板管理器组件
 */
const ScriptTemplates: React.FC<ScriptTemplatesProps> = ({
  visible,
  onClose,
  onSelectTemplate
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [previewTemplate, setPreviewTemplate] = useState<ScriptTemplate | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'rating' | 'downloads' | 'updated'>('name');
  const [favoriteTemplates, setFavoriteTemplates] = useState<Set<string>>(new Set());
  const [recentTemplates, setRecentTemplates] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('all');

  // 加载用户偏好
  useEffect(() => {
    try {
      const savedFavorites = localStorage.getItem('scriptTemplateFavorites');
      const savedRecent = localStorage.getItem('scriptTemplateRecent');

      if (savedFavorites) {
        setFavoriteTemplates(new Set(JSON.parse(savedFavorites)));
      }

      if (savedRecent) {
        setRecentTemplates(JSON.parse(savedRecent));
      }
    } catch (error) {
      console.error('加载用户偏好失败:', error);
    }
  }, []);

  // 保存用户偏好
  const saveFavorites = (favorites: Set<string>) => {
    try {
      localStorage.setItem('scriptTemplateFavorites', JSON.stringify(Array.from(favorites)));
    } catch (error) {
      console.error('保存收藏失败:', error);
    }
  };

  const saveRecent = (recent: string[]) => {
    try {
      localStorage.setItem('scriptTemplateRecent', JSON.stringify(recent));
    } catch (error) {
      console.error('保存最近使用失败:', error);
    }
  };

  // 预定义模板
  const templates: ScriptTemplate[] = [
    {
      id: 'basic-js',
      name: '基础JavaScript脚本',
      description: '包含基本生命周期函数的JavaScript脚本模板',
      category: 'basic',
      type: 'javascript',
      tags: ['基础', '生命周期'],
      content: `// 基础JavaScript脚本
// 在这里编写您的脚本代码

// 生命周期函数
function onStart() {
    console.log('脚本开始执行');
    // 初始化代码
}

function onUpdate(deltaTime) {
    // 每帧更新代码
}

function onDestroy() {
    console.log('脚本销毁');
    // 清理代码
}

// 导出生命周期函数
export { onStart, onUpdate, onDestroy };`,
      author: 'DL Engine',
      version: '1.0.0'
    },
    {
      id: 'basic-ts',
      name: '基础TypeScript脚本',
      description: '包含类型定义的TypeScript脚本模板',
      category: 'basic',
      type: 'typescript',
      tags: ['基础', 'TypeScript', '类型安全'],
      content: `// 基础TypeScript脚本
import { Entity, Component } from 'dl-engine';

// 脚本配置接口
interface ScriptConfig {
    enabled: boolean;
    debug: boolean;
}

// 默认配置
const config: ScriptConfig = {
    enabled: true,
    debug: false
};

// 生命周期函数
export function onStart(): void {
    if (config.debug) {
        console.log('脚本开始执行');
    }
    // 初始化代码
}

export function onUpdate(deltaTime: number): void {
    if (!config.enabled) return;
    // 每帧更新代码
}

export function onDestroy(): void {
    if (config.debug) {
        console.log('脚本销毁');
    }
    // 清理代码
}`,
      author: 'DL Engine',
      version: '1.0.0'
    },
    {
      id: 'player-controller',
      name: '玩家控制器',
      description: '处理玩家输入和移动的脚本',
      category: 'gameplay',
      type: 'javascript',
      tags: ['玩家', '控制', '输入', '移动'],
      content: `// 玩家控制器脚本
import { InputSystem, Vector3 } from 'dl-engine';

let inputSystem = null;
let transform = null;
let moveSpeed = 5.0;
let rotateSpeed = 90.0;

function onStart() {
    // 获取输入系统
    inputSystem = engine.getSystem('InputSystem');
    
    // 获取变换组件
    transform = entity.getTransform();
    
    console.log('玩家控制器初始化完成');
}

function onUpdate(deltaTime) {
    if (!inputSystem || !transform) return;
    
    // 获取输入
    const moveX = inputSystem.getAxis('Horizontal');
    const moveZ = inputSystem.getAxis('Vertical');
    const rotate = inputSystem.getAxis('Mouse X');
    
    // 移动
    if (moveX !== 0 || moveZ !== 0) {
        const moveDirection = new Vector3(moveX, 0, moveZ).normalize();
        const moveDistance = moveSpeed * deltaTime;
        transform.translate(moveDirection.multiplyScalar(moveDistance));
    }
    
    // 旋转
    if (rotate !== 0) {
        const rotateAmount = rotate * rotateSpeed * deltaTime;
        transform.rotateY(rotateAmount * Math.PI / 180);
    }
}

export { onStart, onUpdate };`,
      author: 'DL Engine',
      version: '1.0.0'
    },
    {
      id: 'ui-manager',
      name: 'UI管理器',
      description: '管理用户界面元素的脚本',
      category: 'ui',
      type: 'typescript',
      tags: ['UI', '界面', '管理'],
      content: `// UI管理器脚本
import { UISystem, Entity } from 'dl-engine';

interface UIElement {
    id: string;
    element: any;
    visible: boolean;
}

class UIManager {
    private uiSystem: UISystem | null = null;
    private elements: Map<string, UIElement> = new Map();
    
    public initialize(): void {
        this.uiSystem = engine.getSystem('UISystem') as UISystem;
        this.setupUI();
    }
    
    private setupUI(): void {
        // 创建主菜单
        this.createElement('mainMenu', 'Panel', {
            position: { x: 0, y: 0 },
            size: { width: 200, height: 300 },
            visible: true
        });
        
        // 创建暂停菜单
        this.createElement('pauseMenu', 'Panel', {
            position: { x: 0, y: 0 },
            size: { width: 300, height: 200 },
            visible: false
        });
    }
    
    public createElement(id: string, type: string, options: any): void {
        if (!this.uiSystem) return;
        
        const element = this.uiSystem.createUI(type, options);
        this.elements.set(id, {
            id,
            element,
            visible: options.visible || false
        });
    }
    
    public showElement(id: string): void {
        const uiElement = this.elements.get(id);
        if (uiElement) {
            uiElement.visible = true;
            // 显示UI元素的逻辑
        }
    }
    
    public hideElement(id: string): void {
        const uiElement = this.elements.get(id);
        if (uiElement) {
            uiElement.visible = false;
            // 隐藏UI元素的逻辑
        }
    }
}

const uiManager = new UIManager();

export function onStart(): void {
    uiManager.initialize();
}

export { uiManager };`,
      author: 'DL Engine',
      version: '1.0.0',
      difficulty: 'intermediate',
      rating: 4.5,
      downloads: 1250,
      lastUpdated: '2024-01-15'
    },
    {
      id: 'physics-controller',
      name: '物理控制器',
      description: '基于物理引擎的角色控制器',
      category: 'physics',
      type: 'typescript',
      tags: ['物理', '控制器', '碰撞', '重力'],
      content: `// 物理控制器脚本
import { PhysicsSystem, RigidBody, Vector3, Collider } from 'dl-engine';

interface PhysicsControllerConfig {
    mass: number;
    friction: number;
    bounciness: number;
    maxSpeed: number;
    jumpForce: number;
}

class PhysicsController {
    private rigidBody: RigidBody | null = null;
    private collider: Collider | null = null;
    private isGrounded: boolean = false;

    private config: PhysicsControllerConfig = {
        mass: 1.0,
        friction: 0.6,
        bounciness: 0.0,
        maxSpeed: 10.0,
        jumpForce: 500.0
    };

    public initialize(): void {
        // 获取刚体组件
        this.rigidBody = entity.getComponent('RigidBody') as RigidBody;
        this.collider = entity.getComponent('Collider') as Collider;

        if (this.rigidBody) {
            this.rigidBody.setMass(this.config.mass);
            this.rigidBody.setFriction(this.config.friction);
            this.rigidBody.setBounciness(this.config.bounciness);
        }

        // 设置碰撞检测
        if (this.collider) {
            this.collider.onCollisionEnter = this.onCollisionEnter.bind(this);
            this.collider.onCollisionExit = this.onCollisionExit.bind(this);
        }
    }

    public move(direction: Vector3): void {
        if (!this.rigidBody) return;

        const force = direction.multiplyScalar(this.config.maxSpeed);
        this.rigidBody.addForce(force);
    }

    public jump(): void {
        if (!this.rigidBody || !this.isGrounded) return;

        const jumpVector = new Vector3(0, this.config.jumpForce, 0);
        this.rigidBody.addImpulse(jumpVector);
    }

    private onCollisionEnter(collision: any): void {
        if (collision.gameObject.tag === 'Ground') {
            this.isGrounded = true;
        }
    }

    private onCollisionExit(collision: any): void {
        if (collision.gameObject.tag === 'Ground') {
            this.isGrounded = false;
        }
    }
}

const controller = new PhysicsController();

export function onStart(): void {
    controller.initialize();
}

export function onUpdate(deltaTime: number): void {
    // 处理输入和移动逻辑
}

export { controller };`,
      author: 'DL Engine',
      version: '1.2.0',
      difficulty: 'advanced',
      rating: 4.8,
      downloads: 890,
      lastUpdated: '2024-01-20'
    },
    {
      id: 'animation-controller',
      name: '动画控制器',
      description: '管理角色动画状态和过渡',
      category: 'animation',
      type: 'typescript',
      tags: ['动画', '状态机', '过渡', '角色'],
      content: `// 动画控制器脚本
import { AnimationSystem, Animator, AnimationClip } from 'dl-engine';

enum AnimationState {
    IDLE = 'idle',
    WALK = 'walk',
    RUN = 'run',
    JUMP = 'jump',
    ATTACK = 'attack'
}

interface AnimationTransition {
    from: AnimationState;
    to: AnimationState;
    condition: () => boolean;
    duration: number;
}

class AnimationController {
    private animator: Animator | null = null;
    private currentState: AnimationState = AnimationState.IDLE;
    private transitions: AnimationTransition[] = [];

    public initialize(): void {
        this.animator = entity.getComponent('Animator') as Animator;
        this.setupTransitions();
        this.playAnimation(AnimationState.IDLE);
    }

    private setupTransitions(): void {
        this.transitions = [
            {
                from: AnimationState.IDLE,
                to: AnimationState.WALK,
                condition: () => this.isMoving() && !this.isRunning(),
                duration: 0.2
            },
            {
                from: AnimationState.WALK,
                to: AnimationState.RUN,
                condition: () => this.isRunning(),
                duration: 0.3
            },
            {
                from: AnimationState.WALK,
                to: AnimationState.IDLE,
                condition: () => !this.isMoving(),
                duration: 0.2
            },
            {
                from: AnimationState.RUN,
                to: AnimationState.WALK,
                condition: () => this.isMoving() && !this.isRunning(),
                duration: 0.2
            }
        ];
    }

    public update(): void {
        // 检查状态转换
        for (const transition of this.transitions) {
            if (transition.from === this.currentState && transition.condition()) {
                this.transitionTo(transition.to, transition.duration);
                break;
            }
        }
    }

    private transitionTo(newState: AnimationState, duration: number): void {
        if (this.currentState === newState) return;

        this.currentState = newState;
        this.playAnimation(newState, duration);
    }

    private playAnimation(state: AnimationState, crossfadeDuration: number = 0): void {
        if (!this.animator) return;

        if (crossfadeDuration > 0) {
            this.animator.crossFade(state, crossfadeDuration);
        } else {
            this.animator.play(state);
        }
    }

    private isMoving(): boolean {
        // 检查移动输入或速度
        return false; // 实现具体逻辑
    }

    private isRunning(): boolean {
        // 检查跑步输入
        return false; // 实现具体逻辑
    }

    public getCurrentState(): AnimationState {
        return this.currentState;
    }

    public playOneShot(animationName: string): void {
        if (this.animator) {
            this.animator.playOneShot(animationName);
        }
    }
}

const animController = new AnimationController();

export function onStart(): void {
    animController.initialize();
}

export function onUpdate(deltaTime: number): void {
    animController.update();
}

export { animController, AnimationState };`,
      author: 'DL Engine',
      version: '1.1.0',
      difficulty: 'intermediate',
      rating: 4.6,
      downloads: 1100,
      lastUpdated: '2024-01-18'
    }
  ];

  // 分类列表
  const categories = [
    { key: 'all', label: '全部' },
    { key: 'basic', label: '基础' },
    { key: 'gameplay', label: '游戏玩法' },
    { key: 'ui', label: '用户界面' },
    { key: 'physics', label: '物理' },
    { key: 'animation', label: '动画' },
    { key: 'audio', label: '音频' }
  ];

  // 切换收藏
  const toggleFavorite = (templateId: string) => {
    const newFavorites = new Set(favoriteTemplates);
    if (newFavorites.has(templateId)) {
      newFavorites.delete(templateId);
      message.info('已取消收藏');
    } else {
      newFavorites.add(templateId);
      message.success('已添加到收藏');
    }
    setFavoriteTemplates(newFavorites);
    saveFavorites(newFavorites);
  };

  // 添加到最近使用
  const addToRecent = (templateId: string) => {
    const newRecent = [templateId, ...recentTemplates.filter(id => id !== templateId)].slice(0, 10);
    setRecentTemplates(newRecent);
    saveRecent(newRecent);
  };

  // 过滤模板
  const getFilteredTemplates = () => {
    let filtered = templates.filter(template => {
      const matchesSearch = searchText === '' ||
        template.name.toLowerCase().includes(searchText.toLowerCase()) ||
        template.description.toLowerCase().includes(searchText.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });

    // 根据活动标签页进一步过滤
    switch (activeTab) {
      case 'favorites':
        filtered = filtered.filter(template => favoriteTemplates.has(template.id));
        break;
      case 'recent':
        filtered = filtered.filter(template => recentTemplates.includes(template.id));
        break;
      case 'all':
      default:
        break;
    }

    return sortTemplates(filtered);
  };

  const filteredTemplates = getFilteredTemplates();

  // 排序模板
  const sortTemplates = (templates: ScriptTemplate[]) => {
    return [...templates].sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'downloads':
          return (b.downloads || 0) - (a.downloads || 0);
        case 'updated':
          return new Date(b.lastUpdated || '').getTime() - new Date(a.lastUpdated || '').getTime();
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });
  };

  // 处理模板选择
  const handleSelectTemplate = (template: ScriptTemplate) => {
    addToRecent(template.id);
    onSelectTemplate(template);
    onClose();
    message.success(`已选择模板: ${template.name}`);
  };

  // 处理模板预览
  const handlePreviewTemplate = (template: ScriptTemplate) => {
    setPreviewTemplate(template);
  };

  // 渲染模板卡片
  const renderTemplateCard = (template: ScriptTemplate) => {
    const isFavorite = favoriteTemplates.has(template.id);
    const isRecent = recentTemplates.includes(template.id);

    return (
      <Card
        key={template.id}
        size="small"
        title={
          <Space>
            {template.type === 'visual_script' ? <ApartmentOutlined /> : <CodeOutlined />}
            {template.name}
            {isRecent && <Badge dot color="blue" />}
            {template.difficulty && (
              <Tag color={
                template.difficulty === 'beginner' ? 'green' :
                template.difficulty === 'intermediate' ? 'orange' : 'red'
              }>
                {template.difficulty === 'beginner' ? '初级' :
                 template.difficulty === 'intermediate' ? '中级' : '高级'}
              </Tag>
            )}
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={isFavorite ? '取消收藏' : '添加收藏'}>
              <Button
                type="text"
                size="small"
                icon={isFavorite ? <StarFilled style={{ color: '#fadb14' }} /> : <StarOutlined />}
                onClick={() => toggleFavorite(template.id)}
              />
            </Tooltip>
            <Tooltip title="预览">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handlePreviewTemplate(template)}
              />
            </Tooltip>
            <Button
              type="primary"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => handleSelectTemplate(template)}
            >
              使用
            </Button>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <Paragraph ellipsis={{ rows: 2 }}>
          {template.description}
        </Paragraph>

        <Space wrap style={{ marginBottom: 8 }}>
          {template.tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </Space>

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            {template.rating && (
              <Space>
                <Rate disabled defaultValue={template.rating} style={{ fontSize: '12px' }} />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {template.rating}
                </Text>
              </Space>
            )}
          </div>
          <div>
            {template.downloads && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                <DownloadOutlined /> {template.downloads}
              </Text>
            )}
          </div>
        </div>

        {template.author && (
          <div style={{ marginTop: 8 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <UserOutlined /> {template.author} | v{template.version}
              {template.lastUpdated && ` | ${template.lastUpdated}`}
            </Text>
          </div>
        )}
      </Card>
    );
  };

  return (
    <>
      <Modal
        title="选择脚本模板"
        open={visible}
        onCancel={onClose}
        width={1000}
        footer={null}
        style={{ top: 20 }}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>
            <Input
              placeholder="搜索模板..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
              allowClear
              style={{ flex: 1 }}
            />
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: 120 }}
              placeholder="排序方式"
            >
              <Select.Option value="name">名称</Select.Option>
              <Select.Option value="rating">评分</Select.Option>
              <Select.Option value="downloads">下载量</Select.Option>
              <Select.Option value="updated">更新时间</Select.Option>
            </Select>
          </div>

          <Space wrap>
            {categories.map(category => (
              <Button
                key={category.key}
                type={selectedCategory === category.key ? 'primary' : 'default'}
                size="small"
                onClick={() => setSelectedCategory(category.key)}
                icon={<TagOutlined />}
              >
                {category.label}
              </Button>
            ))}
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <Tabs.TabPane
            tab={
              <span>
                <TagOutlined /> 全部模板
                <Badge count={templates.length} style={{ marginLeft: 8 }} />
              </span>
            }
            key="all"
          >
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {filteredTemplates.map(renderTemplateCard)}

              {filteredTemplates.length === 0 && (
                <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                  没有找到匹配的模板
                </div>
              )}
            </div>
          </Tabs.TabPane>

          <Tabs.TabPane
            tab={
              <span>
                <StarFilled style={{ color: '#fadb14' }} /> 收藏
                <Badge count={favoriteTemplates.size} style={{ marginLeft: 8 }} />
              </span>
            }
            key="favorites"
          >
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {filteredTemplates.map(renderTemplateCard)}

              {filteredTemplates.length === 0 && (
                <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                  {favoriteTemplates.size === 0 ? '还没有收藏的模板' : '没有找到匹配的收藏模板'}
                </div>
              )}
            </div>
          </Tabs.TabPane>

          <Tabs.TabPane
            tab={
              <span>
                <ClockCircleOutlined /> 最近使用
                <Badge count={recentTemplates.length} style={{ marginLeft: 8 }} />
              </span>
            }
            key="recent"
          >
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {filteredTemplates.map(renderTemplateCard)}

              {filteredTemplates.length === 0 && (
                <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                  {recentTemplates.length === 0 ? '还没有最近使用的模板' : '没有找到匹配的最近使用模板'}
                </div>
              )}
            </div>
          </Tabs.TabPane>
        </Tabs>
      </Modal>
      
      {/* 模板预览模态框 */}
      <Modal
        title={`预览: ${previewTemplate?.name}`}
        open={!!previewTemplate}
        onCancel={() => setPreviewTemplate(null)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setPreviewTemplate(null)}>
            关闭
          </Button>,
          <Button 
            key="use" 
            type="primary" 
            onClick={() => previewTemplate && handleSelectTemplate(previewTemplate)}
          >
            使用此模板
          </Button>
        ]}
      >
        {previewTemplate && (
          <div>
            <Paragraph>{previewTemplate.description}</Paragraph>
            <Divider />
            <pre style={{ 
              backgroundColor: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '4px',
              fontSize: '12px',
              maxHeight: '300px',
              overflow: 'auto'
            }}>
              {previewTemplate.content}
            </pre>
          </div>
        )}
      </Modal>
    </>
  );
};

export default ScriptTemplates;
