/**
 * 地形生成工具组件
 * 用于生成地形
 */
import React, { useState, useRef } from 'react';
import {
  Form,
  InputNumber,
  Button,
  Select,
  Slider,
  Divider,
  Card,
  Row,
  Col,
  Tooltip,
  Switch,
  Typography,
  Upload,
  Progress,
  message,
  Dropdown
} from 'antd';
import {
  UploadOutlined,
  SyncOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SaveOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  FileImageOutlined,
  DownOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
// 移除引擎直接导入
// 移除引擎直接导入
// 移除引擎直接导入
// 移除引擎直接导入
import './TerrainGenerationTool.less';
import TerrainPreview from './TerrainPreview';
import TerrainTexturePreview from './TerrainTexturePreview';
import { TerrainGenerationAlgorithm } from './TerrainGenerationEnums';
import TerrainAlgorithmParams from './TerrainAlgorithmParams';

// 类型定义
interface ThermalErosionParams {
  iterations: number;
  strength: number;
  slopeThreshold: number;
  depositionRate: number;
}

interface HydraulicErosionParams {
  iterations: number;
  droplets: number;
  capacity: number;
  erosionStrength: number;
  depositionStrength: number;
  evaporationRate: number;
  inertia: number;
  minSlope: number;
  gravity: number;
}

interface RiverGenerationParams {
  count: number;
  width: number;
  depth: number;
  sinuosity: number;
  branchProbability: number;
  minLength: number;
  maxLength: number;
  seed: number;
}

interface MountainGenerationParams {
  count: number;
  height: number;
  width: number;
  roughness: number;
  sharpness: number;
  seed: number;
}

interface CanyonGenerationParams {
  count: number;
  depth: number;
  width: number;
  sinuosity: number;
  roughness: number;
  seed: number;
}

interface CaveGenerationParams {
  count: number;
  size: number;
  depth: number;
  complexity: number;
  connectionProbability: number;
  seed: number;
}

interface CliffGenerationParams {
  count: number;
  height: number;
  width: number;
  steepness: number;
  roughness: number;
  seed: number;
}

interface VolcanoGenerationParams {
  count: number;
  height: number;
  radius: number;
  craterSize: number;
  craterDepth: number;
  seed: number;
}

interface UndergroundRiverParams {
  count: number;
  width: number;
  depth: number;
  sinuosity: number;
  branchProbability: number;
  minLength: number;
  maxLength: number;
  minDepthRatio: number;
  maxDepthRatio: number;
  caveProbability: number;
  seed: number;
}

enum LakeShapeType {
  CIRCULAR = 'circular',
  IRREGULAR = 'irregular',
  CAVERN = 'cavern',
  CONNECTED = 'connected',
  BRANCHING = 'branching'
}

interface UndergroundLakeParams {
  count: number;
  minRadius: number;
  maxRadius: number;
  depth: number;
  complexity: number;
  depthVariation: number;
  caveProbability: number;
  riverProbability: number;
  minDepthRatio: number;
  maxDepthRatio: number;
  shapeType: LakeShapeType;
  generateStalactites: boolean;
  stalactiteDensity: number;
  generateWaterfall: boolean;
  waterfallHeight: number;
  generateHotSpring: boolean;
  hotSpringTemperature: number;
  seed: number;
}

enum TerrainFeatureType {
  HILLS = 'hills',
  MOUNTAIN = 'mountain',
  RIVER = 'river',
  ISLAND = 'island',
  DESERT = 'desert',
  CANYON = 'canyon',
  CLIFF = 'cliff',
  VOLCANO = 'volcano'
}

interface TerrainFeatureCombinationParams {
  baseTerrainType: TerrainFeatureType;
  baseTerrainParams: any;
  features: Array<{
    type: TerrainFeatureType;
    params: any;
    weight: number;
  }>;
  seed: number;
}

interface TerrainGenerationParams {
  seed: number;
  scale: number;
  persistence: number;
  octaves: number;
  frequency: number;
  amplitude: number;
  erosionIterations: number;
  erosionStrength: number;
}

const { Option } = Select;
const { Text } = Typography;
const { Dragger } = Upload;



/**
 * 地形生成工具属性
 */
interface TerrainGenerationToolProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 地形修改回调 */
  onTerrainModified?: () => void;
  /** 操作回调 */
  onOperation?: (operation: any) => void;
}

/**
 * 地形生成工具组件
 */
const TerrainGenerationTool: React.FC<TerrainGenerationToolProps> = ({
  entityId,
  editable = true,
  onTerrainModified,
  onOperation
}) => {
  const { t } = useTranslation();
  // const dispatch = useDispatch(); // 暂时未使用，保留以备将来需要

  // 从Redux获取地形数据
  const terrainData = useSelector((state: RootState) => {
    if (!entityId) return null;
    const entity = state.scene.entities.find(entity => entity.id === entityId);
    return entity?.components?.TerrainComponent || null;
  });

  // 状态
  const [algorithm, setAlgorithm] = useState<TerrainGenerationAlgorithm>(TerrainGenerationAlgorithm.PERLIN);
  const [seed, setSeed] = useState<number>(Math.random() * 1000);
  const [scale, setScale] = useState<number>(100);
  const [persistence, setPersistence] = useState<number>(0.5);
  const [octaves, setOctaves] = useState<number>(6);
  const [frequency, setFrequency] = useState<number>(0.01);
  const [amplitude, setAmplitude] = useState<number>(1.0);
  const [erosionIterations, setErosionIterations] = useState<number>(1000);
  const [erosionStrength, setErosionStrength] = useState<number>(0.2);

  // 热侵蚀参数（暂时未使用）
  const [thermalErosionParams] = useState<ThermalErosionParams>({
    iterations: 1000,
    strength: 0.2,
    slopeThreshold: 0.05,
    depositionRate: 0.3
  });

  // 水侵蚀参数（暂时未使用）
  const [hydraulicErosionParams] = useState<HydraulicErosionParams>({
    iterations: 1000,
    droplets: 5000,
    capacity: 4,
    erosionStrength: 0.3,
    depositionStrength: 0.3,
    evaporationRate: 0.02,
    inertia: 0.05,
    minSlope: 0.01,
    gravity: 4
  });

  // 河流参数（暂时未使用）
  const [riverParams] = useState<RiverGenerationParams>({
    count: 5,
    width: 10,
    depth: 0.2,
    sinuosity: 0.5,
    branchProbability: 0.2,
    minLength: 50,
    maxLength: 200,
    seed: Math.random() * 1000
  });

  // 山脉参数（暂时未使用）
  const [mountainParams] = useState<MountainGenerationParams>({
    count: 3,
    height: 0.5,
    width: 20,
    roughness: 0.5,
    sharpness: 2,
    seed: Math.random() * 1000
  });

  // 峡谷参数（暂时未使用）
  const [canyonParams] = useState<CanyonGenerationParams>({
    count: 2,
    depth: 0.3,
    width: 15,
    sinuosity: 0.7,
    roughness: 0.4,
    seed: Math.random() * 1000
  });

  // 洞穴参数（暂时未使用）
  const [caveParams] = useState<CaveGenerationParams>({
    count: 3,
    size: 20,
    depth: 0.4,
    complexity: 0.6,
    connectionProbability: 0.3,
    seed: Math.random() * 1000
  });

  // 悬崖参数（暂时未使用）
  const [cliffParams] = useState<CliffGenerationParams>({
    count: 2,
    height: 0.4,
    width: 25,
    steepness: 2.5,
    roughness: 0.5,
    seed: Math.random() * 1000
  });

  // 火山参数（暂时未使用）
  const [volcanoParams] = useState<VolcanoGenerationParams>({
    count: 1,
    height: 0.8,
    radius: 100,
    craterSize: 30,
    craterDepth: 0.3,
    seed: Math.random() * 1000
  });

  // 地下河参数
  const [undergroundRiverParams, setUndergroundRiverParams] = useState<UndergroundRiverParams>({
    count: 3,
    width: 5,
    depth: 0.2,
    sinuosity: 0.6,
    branchProbability: 0.3,
    minLength: 30,
    maxLength: 100,
    minDepthRatio: 0.3,
    maxDepthRatio: 0.7,
    caveProbability: 0.5,
    seed: Math.random() * 1000
  });

  // 地下湖泊参数
  const [undergroundLakeParams, setUndergroundLakeParams] = useState<UndergroundLakeParams>({
    count: 2,
    minRadius: 15,
    maxRadius: 40,
    depth: 0.3,
    complexity: 0.6,
    depthVariation: 0.4,
    caveProbability: 0.5,
    riverProbability: 0.5,
    minDepthRatio: 0.3,
    maxDepthRatio: 0.7,
    shapeType: LakeShapeType.CIRCULAR,
    generateStalactites: false,
    stalactiteDensity: 0.5,
    generateWaterfall: false,
    waterfallHeight: 10,
    generateHotSpring: false,
    hotSpringTemperature: 80,
    seed: Math.random() * 1000
  });

  // 特征组合参数
  const [featureCombinationParams, setFeatureCombinationParams] = useState<TerrainFeatureCombinationParams>({
    baseTerrainType: TerrainFeatureType.HILLS,
    baseTerrainParams: {
      scale: 80,
      persistence: 0.4,
      octaves: 5,
      frequency: 0.008,
      amplitude: 0.8
    },
    features: [
      {
        type: TerrainFeatureType.MOUNTAIN,
        params: {
          count: 2,
          height: 0.5,
          width: 20,
          roughness: 0.5,
          sharpness: 2,
          seed: Math.random() * 1000
        },
        weight: 0.8
      },
      {
        type: TerrainFeatureType.RIVER,
        params: {
          count: 3,
          width: 8,
          depth: 0.15,
          sinuosity: 0.6,
          branchProbability: 0.2,
          minLength: 50,
          maxLength: 200,
          seed: Math.random() * 1000
        },
        weight: 0.7
      }
    ],
    seed: Math.random() * 1000
  });

  const [heightMapFile, setHeightMapFile] = useState<any>(null);
  const [previewEnabled, setPreviewEnabled] = useState<boolean>(true);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [generationProgress, setGenerationProgress] = useState<number>(0);
  const [presets, setPresets] = useState<any[]>([
    { name: t('terrain.generation.presets.mountains'), params: { algorithm: TerrainGenerationAlgorithm.FRACTAL, scale: 150, persistence: 0.65, octaves: 8, frequency: 0.01, amplitude: 1.2, erosionIterations: 2000, erosionStrength: 0.3 } },
    { name: t('terrain.generation.presets.hills'), params: { algorithm: TerrainGenerationAlgorithm.PERLIN, scale: 80, persistence: 0.4, octaves: 5, frequency: 0.008, amplitude: 0.8, erosionIterations: 1000, erosionStrength: 0.15 } },
    { name: t('terrain.generation.presets.plains'), params: { algorithm: TerrainGenerationAlgorithm.PERLIN, scale: 30, persistence: 0.3, octaves: 4, frequency: 0.005, amplitude: 0.5, erosionIterations: 500, erosionStrength: 0.1 } },
    { name: t('terrain.generation.presets.islands'), params: { algorithm: TerrainGenerationAlgorithm.MULTIFRACTAL, scale: 100, persistence: 0.55, octaves: 7, frequency: 0.012, amplitude: 1.0, erosionIterations: 1500, erosionStrength: 0.25 } },
    { name: t('terrain.generation.presets.rivers'), params: { algorithm: TerrainGenerationAlgorithm.RIVER, riverParams: { count: 5, width: 10, depth: 0.2, sinuosity: 0.5, branchProbability: 0.2, minLength: 50, maxLength: 200, seed: 123 } } },
    { name: t('terrain.generation.presets.canyons'), params: { algorithm: TerrainGenerationAlgorithm.CANYON, canyonParams: { count: 2, depth: 0.3, width: 15, sinuosity: 0.7, roughness: 0.4, seed: 456 } } },
    { name: t('terrain.generation.presets.caves'), params: { algorithm: TerrainGenerationAlgorithm.CAVE, caveParams: { count: 3, size: 20, depth: 0.4, complexity: 0.6, connectionProbability: 0.3, seed: 789 } } },
    { name: t('terrain.generation.presets.cliffs'), params: { algorithm: TerrainGenerationAlgorithm.CLIFF, cliffParams: { count: 2, height: 0.4, width: 25, steepness: 2.5, roughness: 0.5, seed: 234 } } },
    { name: t('terrain.generation.presets.volcanoes'), params: { algorithm: TerrainGenerationAlgorithm.VOLCANO, volcanoParams: { count: 1, height: 0.8, radius: 100, craterSize: 30, craterDepth: 0.3, seed: 567 } } },
    { name: t('terrain.generation.presets.undergroundRivers'), params: { algorithm: TerrainGenerationAlgorithm.UNDERGROUND_RIVER, undergroundRiverParams: { count: 3, width: 5, depth: 0.2, sinuosity: 0.6, branchProbability: 0.3, minLength: 30, maxLength: 100, minDepthRatio: 0.3, maxDepthRatio: 0.7, caveProbability: 0.5, seed: 678 } } },
    { name: t('terrain.generation.presets.undergroundLakes'), params: { algorithm: TerrainGenerationAlgorithm.UNDERGROUND_LAKE, undergroundLakeParams: { count: 2, minRadius: 15, maxRadius: 40, depth: 0.3, complexity: 0.6, depthVariation: 0.4, caveProbability: 0.5, riverProbability: 0.5, minDepthRatio: 0.3, maxDepthRatio: 0.7, shapeType: LakeShapeType.CIRCULAR, seed: 789 } } },
    { name: t('terrain.generation.presets.undergroundLakesWithStalactites'), params: { algorithm: TerrainGenerationAlgorithm.UNDERGROUND_LAKE, undergroundLakeParams: { count: 2, minRadius: 20, maxRadius: 50, depth: 0.4, complexity: 0.7, depthVariation: 0.5, caveProbability: 0.6, riverProbability: 0.4, minDepthRatio: 0.4, maxDepthRatio: 0.8, shapeType: LakeShapeType.IRREGULAR, generateStalactites: true, stalactiteDensity: 0.7, seed: 890 } } },
    { name: t('terrain.generation.presets.undergroundLakesWithWaterfall'), params: { algorithm: TerrainGenerationAlgorithm.UNDERGROUND_LAKE, undergroundLakeParams: { count: 1, minRadius: 25, maxRadius: 60, depth: 0.4, complexity: 0.8, depthVariation: 0.6, caveProbability: 0.7, riverProbability: 0.6, minDepthRatio: 0.5, maxDepthRatio: 0.9, shapeType: LakeShapeType.CAVERN, generateWaterfall: true, waterfallHeight: 15, seed: 901 } } },
    { name: t('terrain.generation.presets.undergroundHotSprings'), params: { algorithm: TerrainGenerationAlgorithm.UNDERGROUND_LAKE, undergroundLakeParams: { count: 3, minRadius: 10, maxRadius: 30, depth: 0.3, complexity: 0.5, depthVariation: 0.3, caveProbability: 0.4, riverProbability: 0.3, minDepthRatio: 0.2, maxDepthRatio: 0.6, shapeType: LakeShapeType.CONNECTED, generateHotSpring: true, hotSpringTemperature: 90, seed: 912 } } },
    { name: t('terrain.generation.presets.complexCaveSystem'), params: { algorithm: TerrainGenerationAlgorithm.UNDERGROUND_LAKE, undergroundLakeParams: { count: 5, minRadius: 15, maxRadius: 45, depth: 0.4, complexity: 0.9, depthVariation: 0.7, caveProbability: 0.8, riverProbability: 0.7, minDepthRatio: 0.3, maxDepthRatio: 0.8, shapeType: LakeShapeType.BRANCHING, generateStalactites: true, stalactiteDensity: 0.6, generateWaterfall: true, waterfallHeight: 12, generateHotSpring: true, hotSpringTemperature: 85, seed: 923 } } },
    { name: t('terrain.generation.presets.desert'), params: { algorithm: TerrainGenerationAlgorithm.DESERT, desertParams: { scale: 100, dunes: 20, duneHeight: 0.3, duneWidth: 30, seed: 890 } } },
    { name: t('terrain.generation.presets.mountainsAndRivers'), params: { algorithm: TerrainGenerationAlgorithm.FEATURE_COMBINATION, featureCombinationParams: {
      baseTerrainType: TerrainFeatureType.HILLS,
      baseTerrainParams: { scale: 80, persistence: 0.4, octaves: 5, frequency: 0.008, amplitude: 0.8 },
      features: [
        { type: TerrainFeatureType.MOUNTAIN, params: { count: 2, height: 0.5, width: 20, roughness: 0.5, sharpness: 2, seed: 123 }, weight: 0.8 },
        { type: TerrainFeatureType.RIVER, params: { count: 3, width: 8, depth: 0.15, sinuosity: 0.6, branchProbability: 0.2, minLength: 50, maxLength: 200, seed: 456 }, weight: 0.7 }
      ],
      seed: 789
    } } },
    { name: t('terrain.generation.presets.volcanoIsland'), params: { algorithm: TerrainGenerationAlgorithm.FEATURE_COMBINATION, featureCombinationParams: {
      baseTerrainType: TerrainFeatureType.ISLAND,
      baseTerrainParams: { radius: 200, height: 0.5, falloff: 0.7, seed: 123 },
      features: [
        { type: TerrainFeatureType.VOLCANO, params: { count: 1, height: 0.8, radius: 80, craterSize: 25, craterDepth: 0.3, seed: 456 }, weight: 1.0 },
        { type: TerrainFeatureType.RIVER, params: { count: 4, width: 5, depth: 0.1, sinuosity: 0.7, branchProbability: 0.3, minLength: 30, maxLength: 150, seed: 789 }, weight: 0.8 }
      ],
      seed: 101
    } } },
    { name: t('terrain.generation.presets.canyonLands'), params: { algorithm: TerrainGenerationAlgorithm.FEATURE_COMBINATION, featureCombinationParams: {
      baseTerrainType: TerrainFeatureType.DESERT,
      baseTerrainParams: { scale: 100, dunes: 10, duneHeight: 0.2, duneWidth: 40, seed: 123 },
      features: [
        { type: TerrainFeatureType.CANYON, params: { count: 3, depth: 0.4, width: 20, sinuosity: 0.8, roughness: 0.6, seed: 456 }, weight: 1.0 },
        { type: TerrainFeatureType.CLIFF, params: { count: 4, height: 0.3, width: 15, steepness: 3.0, roughness: 0.4, seed: 789 }, weight: 0.7 }
      ],
      seed: 202
    } } }
  ]);

  // 引用
  const previewRef = useRef<any>(null);

  // 处理算法变更
  const handleAlgorithmChange = (value: TerrainGenerationAlgorithm) => {
    setAlgorithm(value);
  };

  // 处理种子变更
  const handleSeedChange = (value: number | null) => {
    if (value !== null) {
      setSeed(value);
    }
  };

  // 处理随机种子
  const handleRandomSeed = () => {
    setSeed(Math.random() * 1000);
  };

  // 处理比例变更
  const handleScaleChange = (value: number) => {
    setScale(value);
  };

  // 处理持久度变更
  const handlePersistenceChange = (value: number) => {
    setPersistence(value);
  };

  // 处理八度变更
  const handleOctavesChange = (value: number) => {
    setOctaves(value);
  };

  // 处理频率变更
  const handleFrequencyChange = (value: number) => {
    setFrequency(value);
  };

  // 处理幅度变更
  const handleAmplitudeChange = (value: number) => {
    setAmplitude(value);
  };

  // 处理侵蚀迭代次数变更
  const handleErosionIterationsChange = (value: number) => {
    setErosionIterations(value);
  };

  // 处理侵蚀强度变更
  const handleErosionStrengthChange = (value: number) => {
    setErosionStrength(value);
  };

  // 处理高度图上传
  const handleHeightMapUpload = (file: any) => {
    setHeightMapFile(file);
    return false;
  };

  // 处理导入高度图
  const handleImportHeightMap = async (file: File) => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    try {
      // 显示加载中
      message.loading(t('terrain.generation.importing'), 0);

      // 导入高度图
      const success = await terrainData.importFromHeightMap(file, {
        flipY: false,
        heightScale: 1.0,
        applySmoothing: false
      });

      // 关闭加载中
      message.destroy();

      if (success) {
        message.success(t('terrain.generation.heightMapImported'));
        // 通知地形修改
        if (onTerrainModified) {
          onTerrainModified();
        }
      } else {
        message.error(t('terrain.errors.importFailed'));
      }
    } catch (error) {
      // 关闭加载中
      message.destroy();
      console.error('导入高度图失败:', error);
      message.error(t('terrain.errors.importFailed'));
    }
  };

  // 处理导入JSON
  const handleImportJSON = async (file: File) => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    try {
      // 显示加载中
      message.loading(t('terrain.generation.importing'), 0);

      // 读取文件
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const json = e.target?.result as string;

          // 导入JSON
          const success = terrainData.importFromJSON(json, {
            keepTextures: false,
            keepPhysics: false,
            computeNormals: true,
            applySmoothing: false
          });

          // 关闭加载中
          message.destroy();

          if (success) {
            message.success(t('terrain.generation.jsonImported'));
            // 通知地形修改
            if (onTerrainModified) {
              onTerrainModified();
            }
          } else {
            message.error(t('terrain.errors.importFailed'));
          }
        } catch (error) {
          // 关闭加载中
          message.destroy();
          console.error('导入JSON失败:', error);
          message.error(t('terrain.errors.importFailed'));
        }
      };

      reader.onerror = () => {
        // 关闭加载中
        message.destroy();
        message.error(t('terrain.errors.importFailed'));
      };

      reader.readAsText(file);
    } catch (error) {
      // 关闭加载中
      message.destroy();
      console.error('导入JSON失败:', error);
      message.error(t('terrain.errors.importFailed'));
    }
  };

  // 处理预览启用变更
  const handlePreviewEnabledChange = (checked: boolean) => {
    setPreviewEnabled(checked);
  };

  // 获取当前算法的预览参数
  const getPreviewParams = () => {
    switch (algorithm) {
      case TerrainGenerationAlgorithm.PERLIN:
      case TerrainGenerationAlgorithm.FRACTAL:
      case TerrainGenerationAlgorithm.MULTIFRACTAL:
        return {
          seed,
          scale,
          persistence,
          octaves,
          frequency,
          amplitude
        };
      case TerrainGenerationAlgorithm.THERMAL_EROSION:
        return thermalErosionParams;
      case TerrainGenerationAlgorithm.HYDRAULIC_EROSION:
        return hydraulicErosionParams;
      case TerrainGenerationAlgorithm.RIVER:
        return riverParams;
      case TerrainGenerationAlgorithm.MOUNTAIN:
        return mountainParams;
      case TerrainGenerationAlgorithm.CANYON:
        return canyonParams;
      case TerrainGenerationAlgorithm.CAVE:
        return caveParams;
      case TerrainGenerationAlgorithm.CLIFF:
        return cliffParams;
      case TerrainGenerationAlgorithm.VOLCANO:
        return volcanoParams;
      case TerrainGenerationAlgorithm.UNDERGROUND_RIVER:
        return undergroundRiverParams;
      case TerrainGenerationAlgorithm.UNDERGROUND_LAKE:
        return undergroundLakeParams;
      case TerrainGenerationAlgorithm.FEATURE_COMBINATION:
        return featureCombinationParams;
      default:
        return {};
    }
  };

  // 处理预设选择
  const handlePresetSelect = (presetIndex: number) => {
    const preset = presets[presetIndex];
    if (!preset) return;

    setAlgorithm(preset.params.algorithm);
    setScale(preset.params.scale);
    setPersistence(preset.params.persistence);
    setOctaves(preset.params.octaves);
    setFrequency(preset.params.frequency);
    setAmplitude(preset.params.amplitude);
    setErosionIterations(preset.params.erosionIterations);
    setErosionStrength(preset.params.erosionStrength);
  };

  // 处理保存预设
  const handleSavePreset = () => {
    const presetName = prompt(t('terrain.generation.enterPresetName') as string);
    if (!presetName) return;

    const newPreset = {
      name: presetName,
      params: {
        algorithm,
        scale,
        persistence,
        octaves,
        frequency,
        amplitude,
        erosionIterations,
        erosionStrength
      }
    };

    setPresets([...presets, newPreset]);
    message.success(t('terrain.generation.presetSaved'));
  };

  // 处理生成地形
  const handleGenerateTerrain = () => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    // 开始生成
    setIsGenerating(true);
    setGenerationProgress(0);

    // 创建操作对象
    let operation: any = {
      type: 'GENERATE_TERRAIN',
      algorithm
    };

    // 根据算法类型创建不同的参数
    switch (algorithm) {
      case TerrainGenerationAlgorithm.PERLIN:
      case TerrainGenerationAlgorithm.FRACTAL:
      case TerrainGenerationAlgorithm.DIAMOND_SQUARE:
      case TerrainGenerationAlgorithm.MULTIFRACTAL:
        // 创建基本地形生成参数
        const basicParams: TerrainGenerationParams = {
          seed,
          scale,
          persistence,
          octaves,
          frequency,
          amplitude,
          erosionIterations,
          erosionStrength
        };
        operation.params = basicParams;
        break;

      case TerrainGenerationAlgorithm.THERMAL_EROSION:
        // 热侵蚀参数
        operation.params = thermalErosionParams;
        break;

      case TerrainGenerationAlgorithm.HYDRAULIC_EROSION:
        // 水侵蚀参数
        operation.params = hydraulicErosionParams;
        break;

      case TerrainGenerationAlgorithm.RIVER:
        // 河流参数
        operation.params = riverParams;
        break;

      case TerrainGenerationAlgorithm.MOUNTAIN:
        // 山脉参数
        operation.params = mountainParams;
        break;

      case TerrainGenerationAlgorithm.CANYON:
        // 峡谷参数
        operation.params = canyonParams;
        break;

      case TerrainGenerationAlgorithm.CAVE:
        // 洞穴参数
        operation.params = caveParams;
        break;

      case TerrainGenerationAlgorithm.CLIFF:
        // 悬崖参数
        operation.params = cliffParams;
        break;

      case TerrainGenerationAlgorithm.VOLCANO:
        // 火山参数
        operation.params = volcanoParams;
        break;

      case TerrainGenerationAlgorithm.UNDERGROUND_RIVER:
        // 地下河参数
        operation.params = undergroundRiverParams;
        break;

      case TerrainGenerationAlgorithm.UNDERGROUND_LAKE:
        // 地下湖泊参数
        operation.params = undergroundLakeParams;
        break;

      case TerrainGenerationAlgorithm.PLAIN:
        // 平原参数
        operation.params = { baseHeight: 0.1 };
        break;

      case TerrainGenerationAlgorithm.HILLS:
        // 丘陵参数
        operation.params = {
          scale: 80,
          persistence: 0.4,
          octaves: 5,
          frequency: 0.008,
          amplitude: 0.8,
          seed: seed
        };
        break;

      case TerrainGenerationAlgorithm.DESERT:
        // 沙漠参数
        operation.params = {
          scale: 100,
          dunes: 20,
          duneHeight: 0.3,
          duneWidth: 30,
          seed: seed
        };
        break;

      case TerrainGenerationAlgorithm.ISLAND:
        // 岛屿参数
        operation.params = {
          radius: terrainData.resolution * 0.4,
          height: 0.8,
          falloff: 0.7,
          seed: seed
        };
        break;

      case TerrainGenerationAlgorithm.FEATURE_COMBINATION:
        // 特征组合参数
        operation.params = featureCombinationParams;
        break;

      case TerrainGenerationAlgorithm.HEIGHTMAP:
        // 高度图参数
        operation.heightMapFile = heightMapFile;
        break;
    }

    // 模拟生成进度
    const interval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsGenerating(false);

          // 通知地形修改
          if (onTerrainModified) {
            onTerrainModified();
          }

          // 记录操作
          if (onOperation) {
            onOperation(operation);
          }

          message.success(t('terrain.generation.generationComplete'));
          return 100;
        }
        return prev + 5;
      });
    }, 100);

    // 初始化地形生成算法
    // TerrainGenerationAlgorithms.initialize({
    //   maxWorkers: navigator.hardwareConcurrency || 4,
    //   enableMultithreading: true,
    //   debug: true
    // });

    // 实现实际的地形生成逻辑，根据不同的算法调用不同的生成函数
    const generateTerrain = async () => {
      try {
        switch (algorithm) {
          case TerrainGenerationAlgorithm.PERLIN:
            // 调用柏林噪声生成
            // 这里需要实现柏林噪声生成逻辑
            break;
          case TerrainGenerationAlgorithm.THERMAL_EROSION:
            // 调用热侵蚀算法
            // await TerrainGenerationAlgorithms.applyThermalErosion(terrainData, thermalErosionParams);
            console.log('热侵蚀算法生成 - 待实现');
            break;
          case TerrainGenerationAlgorithm.HYDRAULIC_EROSION:
            // 调用水侵蚀算法
            // await TerrainGenerationAlgorithms.applyHydraulicErosion(terrainData, hydraulicErosionParams);
            console.log('水侵蚀算法生成 - 待实现');
            break;
          case TerrainGenerationAlgorithm.RIVER:
            // 调用河流生成算法
            // await TerrainGenerationAlgorithms.generateRivers(terrainData, riverParams);
            console.log('河流生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.MOUNTAIN:
            // 调用山脉生成算法
            // await TerrainGenerationAlgorithms.generateMountains(terrainData, mountainParams);
            console.log('山脉生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.CANYON:
            // 调用峡谷生成算法
            // await TerrainGenerationAlgorithms.generateCanyons(terrainData, canyonParams);
            console.log('峡谷生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.CAVE:
            // 调用洞穴生成算法
            // await TerrainGenerationAlgorithms.generateCaves(terrainData, caveParams);
            console.log('洞穴生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.CLIFF:
            // 调用悬崖生成算法
            // await TerrainGenerationAlgorithms.generateCliffs(terrainData, cliffParams);
            console.log('悬崖生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.VOLCANO:
            // 调用火山生成算法
            // await TerrainGenerationAlgorithms.generateVolcanoes(terrainData, volcanoParams);
            console.log('火山生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.UNDERGROUND_RIVER:
            // 调用地下河生成算法
            // TerrainGenerationAlgorithms.generateUndergroundRivers(terrainData, undergroundRiverParams);
            console.log('地下河生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.UNDERGROUND_LAKE:
            // 调用地下湖泊生成算法
            // TerrainGenerationAlgorithms.generateUndergroundLakes(terrainData, undergroundLakeParams);
            console.log('地下湖泊生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.PLAIN:
            // 调用平原生成算法
            // TerrainGenerationAlgorithms.generateFlatTerrain(terrainData, 0.1);
            console.log('平原生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.HILLS:
            // 调用丘陵生成算法
            // TerrainGenerationAlgorithms.generateHillyTerrain(terrainData, operation.params);
            console.log('丘陵生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.DESERT:
            // 调用沙漠生成算法
            // TerrainGenerationAlgorithms.generateDesertTerrain(terrainData, operation.params);
            console.log('沙漠生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.ISLAND:
            // 调用岛屿生成算法
            // TerrainGenerationAlgorithms.generateIslandTerrain(terrainData, operation.params);
            console.log('岛屿生成算法 - 待实现');
            break;
          case TerrainGenerationAlgorithm.FEATURE_COMBINATION:
            // 调用特征组合生成算法
            // await TerrainGenerationAlgorithms.generateTerrainFeatureCombination(terrainData, featureCombinationParams);
            console.log('特征组合生成算法 - 待实现');
            break;
        }
      } catch (error) {
        console.error('地形生成错误:', error);
        message.error(t('terrain.errors.generationFailed'));
      }
    };

    // 执行地形生成
    generateTerrain();
  };

  // 处理取消生成
  const handleCancelGeneration = () => {
    // 这里需要实现取消生成逻辑
    setIsGenerating(false);
    setGenerationProgress(0);
    message.info(t('terrain.generation.generationCancelled'));
  };

  // 处理导出高度图
  const handleExportHeightMap = async () => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    try {
      // 导出高度图
      const blob = await terrainData.exportToHeightMap({
        format: 'png',
        width: terrainData.resolution,
        height: terrainData.resolution,
        flipY: false,
        normalize: true
      });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `terrain_heightmap_${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(t('terrain.generation.heightMapExported'));
    } catch (error) {
      console.error('导出高度图失败:', error);
      message.error(t('terrain.errors.exportFailed'));
    }
  };

  // 处理导出JSON
  const handleExportJSON = () => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    try {
      // 导出JSON
      const json = terrainData.exportToJSON({
        includeTextures: true,
        includeNormals: true,
        includePhysics: true,
        prettyPrint: true
      });

      // 创建Blob
      const blob = new Blob([json], { type: 'application/json' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `terrain_data_${Date.now()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(t('terrain.generation.jsonExported'));
    } catch (error) {
      console.error('导出JSON失败:', error);
      message.error(t('terrain.errors.exportFailed'));
    }
  };

  // 获取算法选项
  const algorithmOptions = [
    { value: TerrainGenerationAlgorithm.PERLIN, label: t('terrain.generation.algorithms.perlin') },
    { value: TerrainGenerationAlgorithm.FRACTAL, label: t('terrain.generation.algorithms.fractal') },
    { value: TerrainGenerationAlgorithm.DIAMOND_SQUARE, label: t('terrain.generation.algorithms.diamondSquare') },
    { value: TerrainGenerationAlgorithm.MULTIFRACTAL, label: t('terrain.generation.algorithms.multifractal') },
    { value: TerrainGenerationAlgorithm.HEIGHTMAP, label: t('terrain.generation.algorithms.heightmap') },
    { value: TerrainGenerationAlgorithm.THERMAL_EROSION, label: t('terrain.generation.algorithms.thermalErosion') },
    { value: TerrainGenerationAlgorithm.HYDRAULIC_EROSION, label: t('terrain.generation.algorithms.hydraulicErosion') },
    { value: TerrainGenerationAlgorithm.RIVER, label: t('terrain.generation.algorithms.river') },
    { value: TerrainGenerationAlgorithm.UNDERGROUND_RIVER, label: t('terrain.generation.algorithms.undergroundRiver') },
    { value: TerrainGenerationAlgorithm.MOUNTAIN, label: t('terrain.generation.algorithms.mountain') },
    { value: TerrainGenerationAlgorithm.CANYON, label: t('terrain.generation.algorithms.canyon') },
    { value: TerrainGenerationAlgorithm.CAVE, label: t('terrain.generation.algorithms.cave') },
    { value: TerrainGenerationAlgorithm.CLIFF, label: t('terrain.generation.algorithms.cliff') },
    { value: TerrainGenerationAlgorithm.VOLCANO, label: t('terrain.generation.algorithms.volcano') },
    { value: TerrainGenerationAlgorithm.PLAIN, label: t('terrain.generation.algorithms.plain') },
    { value: TerrainGenerationAlgorithm.HILLS, label: t('terrain.generation.algorithms.hills') },
    { value: TerrainGenerationAlgorithm.DESERT, label: t('terrain.generation.algorithms.desert') },
    { value: TerrainGenerationAlgorithm.ISLAND, label: t('terrain.generation.algorithms.island') },
    { value: TerrainGenerationAlgorithm.FEATURE_COMBINATION, label: t('terrain.generation.algorithms.featureCombination') }
  ];

  return (
    <div className="terrain-generation-tool">
      <Row gutter={[16, 16]}>
        <Col span={16}>
          <Card title={t('terrain.generation.parameters')} className="generation-parameters-card">
            <Form layout="vertical">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item label={t('terrain.generation.algorithm')}>
                    <Select
                      value={algorithm}
                      onChange={handleAlgorithmChange}
                      disabled={!editable || isGenerating}
                    >
                      {algorithmOptions.map(option => (
                        <Option key={option.value} value={option.value}>
                          {option.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label={t('terrain.generation.seed')}>
                    <InputNumber
                      value={seed}
                      onChange={handleSeedChange}
                      disabled={!editable || isGenerating}
                      style={{ width: 'calc(100% - 40px)' }}
                    />
                    <Button
                      icon={<SyncOutlined />}
                      onClick={handleRandomSeed}
                      disabled={!editable || isGenerating}
                      style={{ marginLeft: 8 }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {algorithm === TerrainGenerationAlgorithm.UNDERGROUND_RIVER || algorithm === TerrainGenerationAlgorithm.UNDERGROUND_LAKE ? (
                <TerrainAlgorithmParams
                  algorithm={algorithm}
                  editable={editable}
                  isGenerating={isGenerating}
                  undergroundRiverParams={undergroundRiverParams}
                  onUndergroundRiverParamsChange={setUndergroundRiverParams}
                  undergroundLakeParams={undergroundLakeParams}
                  onUndergroundLakeParamsChange={(params) => setUndergroundLakeParams(params as UndergroundLakeParams)}
                />
              ) : algorithm !== TerrainGenerationAlgorithm.HEIGHTMAP ? (
                <>
                  <Form.Item
                    label={
                      <span>
                        {t('terrain.generation.scale')}
                        <Tooltip title={t('terrain.generation.scaleTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={10}
                      max={500}
                      value={scale}
                      onChange={handleScaleChange}
                      disabled={!editable || isGenerating}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <span>
                        {t('terrain.generation.persistence')}
                        <Tooltip title={t('terrain.generation.persistenceTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={0.1}
                      max={1}
                      step={0.01}
                      value={persistence}
                      onChange={handlePersistenceChange}
                      disabled={!editable || isGenerating}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <span>
                        {t('terrain.generation.octaves')}
                        <Tooltip title={t('terrain.generation.octavesTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={1}
                      max={12}
                      step={1}
                      value={octaves}
                      onChange={handleOctavesChange}
                      disabled={!editable || isGenerating}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <span>
                        {t('terrain.generation.frequency')}
                        <Tooltip title={t('terrain.generation.frequencyTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={0.001}
                      max={0.05}
                      step={0.001}
                      value={frequency}
                      onChange={handleFrequencyChange}
                      disabled={!editable || isGenerating}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <span>
                        {t('terrain.generation.amplitude')}
                        <Tooltip title={t('terrain.generation.amplitudeTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={0.1}
                      max={2}
                      step={0.1}
                      value={amplitude}
                      onChange={handleAmplitudeChange}
                      disabled={!editable || isGenerating}
                    />
                  </Form.Item>

                  <Divider>{t('terrain.generation.erosion')}</Divider>

                  <Form.Item
                    label={
                      <span>
                        {t('terrain.generation.erosionIterations')}
                        <Tooltip title={t('terrain.generation.erosionIterationsTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={0}
                      max={5000}
                      step={100}
                      value={erosionIterations}
                      onChange={handleErosionIterationsChange}
                      disabled={!editable || isGenerating}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <span>
                        {t('terrain.generation.erosionStrength')}
                        <Tooltip title={t('terrain.generation.erosionStrengthTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={0}
                      max={1}
                      step={0.01}
                      value={erosionStrength}
                      onChange={handleErosionStrengthChange}
                      disabled={!editable || isGenerating}
                    />
                  </Form.Item>
                </>
              ) : (
                <Form.Item label={t('terrain.generation.heightMap')}>
                  <Dragger
                    name="heightMap"
                    beforeUpload={handleHeightMapUpload}
                    disabled={!editable || isGenerating}
                    showUploadList={false}
                  >
                    {heightMapFile ? (
                      <div>
                        <p className="ant-upload-drag-icon">
                          <FileImageOutlined />
                        </p>
                        <p className="ant-upload-text">{heightMapFile.name}</p>
                      </div>
                    ) : (
                      <div>
                        <p className="ant-upload-drag-icon">
                          <UploadOutlined />
                        </p>
                        <p className="ant-upload-text">{t('terrain.generation.dragHeightMap')}</p>
                        <p className="ant-upload-hint">{t('terrain.generation.heightMapHint')}</p>
                      </div>
                    )}
                  </Dragger>
                </Form.Item>
              )}
            </Form>
          </Card>
        </Col>

        <Col span={8}>
          <Card title={t('terrain.generation.preview')} className="generation-preview-card">
            <div className="preview-container" ref={previewRef}>
              {/* 预览区域 */}
              {algorithm === TerrainGenerationAlgorithm.UNDERGROUND_RIVER ? (
                <TerrainTexturePreview
                  algorithm={algorithm}
                  params={getPreviewParams()}
                  textures={{
                    diffuse: '/textures/terrain/soil_diffuse.jpg',
                    normal: '/textures/terrain/soil_normal.jpg',
                    roughness: '/textures/terrain/soil_roughness.jpg'
                  }}
                  width={300}
                  height={200}
                  enabled={previewEnabled}
                />
              ) : (
                <TerrainPreview
                  algorithm={algorithm}
                  params={getPreviewParams()}
                  width={300}
                  height={200}
                  enabled={previewEnabled}
                  resolution={64}
                />
              )}
            </div>
            <div className="preview-controls">
              <Switch
                checked={previewEnabled}
                onChange={handlePreviewEnabledChange}
                disabled={!editable || isGenerating}
              />
              <Text style={{ marginLeft: 8 }}>{t('terrain.generation.enablePreview')}</Text>
            </div>
          </Card>

          <Card title={t('terrain.generation.presets')} className="generation-presets-card" style={{ marginTop: 16 }}>
            <div className="presets-list">
              {presets.map((preset, index) => (
                <Button
                  key={index}
                  className="preset-button"
                  onClick={() => handlePresetSelect(index)}
                  disabled={!editable || isGenerating}
                >
                  {preset.name}
                </Button>
              ))}
            </div>
            <div className="presets-actions">
              <Button
                icon={<SaveOutlined />}
                onClick={handleSavePreset}
                disabled={!editable || isGenerating}
              >
                {t('terrain.generation.savePreset')}
              </Button>
            </div>
          </Card>

          <Card title={t('terrain.generation.actions')} className="generation-actions-card" style={{ marginTop: 16 }}>
            {isGenerating ? (
              <div className="generation-progress">
                <Progress percent={generationProgress} status="active" />
                <Button
                  type="primary"
                  danger
                  icon={<PauseCircleOutlined />}
                  onClick={handleCancelGeneration}
                  disabled={!editable}
                >
                  {t('terrain.generation.cancel')}
                </Button>
              </div>
            ) : (
              <div className="generation-buttons">
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleGenerateTerrain}
                  disabled={!editable || (algorithm === TerrainGenerationAlgorithm.HEIGHTMAP && !heightMapFile)}
                >
                  {t('terrain.generation.generate')}
                </Button>
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'exportHeightMap',
                        label: t('terrain.generation.exportHeightMap'),
                        icon: <DownloadOutlined />,
                        onClick: handleExportHeightMap
                      },
                      {
                        key: 'exportJSON',
                        label: t('terrain.generation.exportJSON'),
                        icon: <DownloadOutlined />,
                        onClick: handleExportJSON
                      }
                    ]
                  }}
                  disabled={!editable || !terrainData}
                >
                  <Button icon={<DownloadOutlined />}>
                    {t('terrain.generation.export')} <DownOutlined />
                  </Button>
                </Dropdown>
                <Upload
                  accept=".png,.jpg,.jpeg,.raw,.r16,.r32,.asc,.hgt,.ter,.bt,.json"
                  showUploadList={false}
                  beforeUpload={(file) => {
                    const fileExt = file.name.split('.').pop()?.toLowerCase();
                    if (fileExt === 'json') {
                      handleImportJSON(file);
                    } else {
                      handleImportHeightMap(file);
                    }
                    return false;
                  }}
                  disabled={!editable || !terrainData}
                >
                  <Button icon={<UploadOutlined />}>
                    {t('terrain.generation.import')}
                  </Button>
                </Upload>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TerrainGenerationTool;
