/**
 * 地形性能面板
 * 用于显示和控制地形性能优化
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Tabs, Button, Switch, Slider, Space, Divider, Typography, Row, Col, Statistic, Tag } from 'antd';
import {
  DashboardOutlined,
  SettingOutlined,
  LineChartOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  AreaChartOutlined,
  PauseOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// import { useSelector } from 'react-redux';
// import { RootState } from '../../store';
import TerrainBenchmarkPanel from './TerrainBenchmarkPanel';
import './TerrainPerformancePanel.less';

const { TabPane } = Tabs;
const { Text } = Typography;

/**
 * 性能数据接口
 */
interface PerformanceData {
  fps: number;
  memoryUsage: number;
  terrainRenderTime: number;
  visibleTerrainChunks: number;
  terrainTriangles: number;
  terrainVertices: number;
  terrainTextureMemory: number;
  terrainGeometryMemory: number;
}

/**
 * 性能瓶颈类型
 */
enum PerformanceBottleneckType {
  GPU_MEMORY = 'gpu_memory',
  CPU_USAGE = 'cpu_usage',
  DRAW_CALLS = 'draw_calls',
  TEXTURE_MEMORY = 'texture_memory',
  GEOMETRY_COMPLEXITY = 'geometry_complexity'
}

/**
 * 性能监控事件类型
 */
enum PerformanceMonitorEventType {
  PERFORMANCE_DATA_UPDATED = 'performance_data_updated',
  PERFORMANCE_WARNING = 'performance_warning',
  BOTTLENECK_DETECTED = 'bottleneck_detected'
}

/**
 * 性能监控器类（模拟）
 */
class TerrainPerformanceMonitor {
  private static instance: TerrainPerformanceMonitor;
  private listeners: Map<string, Function[]> = new Map();
  private _isRunning = false;

  static getInstance(): TerrainPerformanceMonitor {
    if (!TerrainPerformanceMonitor.instance) {
      TerrainPerformanceMonitor.instance = new TerrainPerformanceMonitor();
    }
    return TerrainPerformanceMonitor.instance;
  }

  configure(_config: any) {
    // 配置监控器
  }

  start() {
    this._isRunning = true;
  }

  stop() {
    this._isRunning = false;
  }

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function) {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }
}

/**
 * 地形性能面板属性
 */
interface TerrainPerformancePanelProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
}

/**
 * 地形性能面板组件
 */
const TerrainPerformancePanel: React.FC<TerrainPerformancePanelProps> = ({
  entityId,
  editable = true
}) => {
  const { t } = useTranslation();

  // 从Redux获取地形数据（暂时保留以备将来使用）
  // const terrainData = useSelector((state: RootState) => {
  //   if (!entityId) return null;
  //   const entity = state.scene.entities.find(e => e.id === entityId);
  //   return entity?.components?.TerrainComponent || null;
  // });

  // 性能监控器
  const performanceMonitor = useRef<TerrainPerformanceMonitor | null>(null);

  // 状态
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [isMonitoring, setIsMonitoring] = useState<boolean>(false);
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [performanceHistory, setPerformanceHistory] = useState<PerformanceData[]>([]);
  const [bottlenecks, setBottlenecks] = useState<{type: PerformanceBottleneckType, message: string}[]>([]);
  const [warnings, setWarnings] = useState<{type: string, message: string, value: number, threshold: number}[]>([]);

  // 配置
  const [updateFrequency, setUpdateFrequency] = useState<number>(1000);
  const [sampleSize, setSampleSize] = useState<number>(60);
  const [recordHistory, setRecordHistory] = useState<boolean>(true);
  const [historyLength, setHistoryLength] = useState<number>(60);
  const [enableWarnings, setEnableWarnings] = useState<boolean>(true);
  const [enableBottleneckDetection, setEnableBottleneckDetection] = useState<boolean>(true);
  const [showSettings, setShowSettings] = useState<boolean>(false);

  // 注册事件监听器
  const registerEventListeners = React.useCallback(() => {
    if (!performanceMonitor.current) return;

    // 性能数据更新事件
    performanceMonitor.current.on(PerformanceMonitorEventType.PERFORMANCE_DATA_UPDATED, (data: PerformanceData) => {
      setPerformanceData(data);

      // 更新历史数据
      if (recordHistory) {
        setPerformanceHistory(prev => {
          const newHistory = [...prev, data];
          if (newHistory.length > historyLength) {
            return newHistory.slice(-historyLength);
          }
          return newHistory;
        });
      }
    });

    // 性能警告事件
    performanceMonitor.current.on(PerformanceMonitorEventType.PERFORMANCE_WARNING, (data: any[]) => {
      setWarnings(data);
    });

    // 性能瓶颈检测事件
    performanceMonitor.current.on(PerformanceMonitorEventType.BOTTLENECK_DETECTED, (type: PerformanceBottleneckType, message: string) => {
      setBottlenecks(prev => {
        // 检查是否已存在相同类型的瓶颈
        const exists = prev.some(b => b.type === type);
        if (exists) {
          return prev;
        }
        return [...prev, { type, message }];
      });
    });
  }, [recordHistory, historyLength]);

  // 取消注册事件监听器
  const unregisterEventListeners = React.useCallback(() => {
    if (!performanceMonitor.current) return;

    performanceMonitor.current.off(PerformanceMonitorEventType.PERFORMANCE_DATA_UPDATED, () => {});
    performanceMonitor.current.off(PerformanceMonitorEventType.PERFORMANCE_WARNING, () => {});
    performanceMonitor.current.off(PerformanceMonitorEventType.BOTTLENECK_DETECTED, () => {});
  }, []);

  // 初始化性能监控器
  useEffect(() => {
    // 获取性能监控器实例
    performanceMonitor.current = TerrainPerformanceMonitor.getInstance();

    // 配置性能监控器
    performanceMonitor.current.configure({
      enabled: isMonitoring,
      updateFrequency,
      sampleSize,
      recordHistory,
      maxHistoryLength: historyLength,
      enableWarnings,
      enableBottleneckDetection
    });

    // 注册事件监听器
    registerEventListeners();

    // 清理函数
    return () => {
      unregisterEventListeners();
    };
  }, [registerEventListeners, unregisterEventListeners, isMonitoring, updateFrequency, sampleSize, recordHistory, historyLength, enableWarnings, enableBottleneckDetection]);

  // 启动监控
  const startMonitoring = () => {
    if (!performanceMonitor.current) return;

    performanceMonitor.current.start();
    setIsMonitoring(true);
  };

  // 停止监控
  const stopMonitoring = () => {
    if (!performanceMonitor.current) return;

    performanceMonitor.current.stop();
    setIsMonitoring(false);
  };

  // 切换监控
  const toggleMonitoring = () => {
    if (isMonitoring) {
      stopMonitoring();
    } else {
      startMonitoring();
    }
  };

  // 重置性能数据
  const resetPerformanceData = () => {
    setPerformanceData(null);
    setPerformanceHistory([]);
    setBottlenecks([]);
    setWarnings([]);
  };

  // 更新配置
  const updateConfig = React.useCallback(() => {
    if (!performanceMonitor.current) return;

    performanceMonitor.current.configure({
      enabled: isMonitoring,
      updateFrequency,
      sampleSize,
      recordHistory,
      maxHistoryLength: historyLength,
      enableWarnings,
      enableBottleneckDetection
    });
  }, [isMonitoring, updateFrequency, sampleSize, recordHistory, historyLength, enableWarnings, enableBottleneckDetection]);

  // 配置变更时更新
  useEffect(() => {
    updateConfig();
  }, [updateConfig]);

  // 获取指标状态颜色
  const getMetricStatusColor = (value: number, threshold: number, isHigherBetter: boolean = false): string => {
    if (isHigherBetter) {
      if (value >= threshold) return '#52c41a'; // 绿色
      if (value >= threshold * 0.7) return '#faad14'; // 黄色
      return '#f5222d'; // 红色
    } else {
      if (value <= threshold) return '#52c41a'; // 绿色
      if (value <= threshold * 1.3) return '#faad14'; // 黄色
      return '#f5222d'; // 红色
    }
  };

  // 渲染概览
  const renderOverview = () => {
    if (!performanceData) {
      return (
        <div className="no-data-message">
          <InfoCircleOutlined />
          <Text>{t('terrain.performance.noData')}</Text>
        </div>
      );
    }

    return (
      <div className="performance-overview">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('terrain.performance.fps')}
                value={performanceData.fps}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(performanceData.fps, 30, true) }}
                prefix={<DashboardOutlined />}
                suffix="FPS"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('terrain.performance.memory')}
                value={performanceData.memoryUsage / (1024 * 1024)}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(performanceData.memoryUsage / (1024 * 1024), 500) }}
                prefix={<AreaChartOutlined />}
                suffix="MB"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('terrain.performance.renderTime')}
                value={performanceData.terrainRenderTime}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(performanceData.terrainRenderTime, 16) }}
                prefix={<LineChartOutlined />}
                suffix="ms"
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title={t('terrain.performance.terrainStats')}>
              <div className="stat-item">
                <Text>{t('terrain.performance.visibleChunks')}</Text>
                <Text>{performanceData.visibleTerrainChunks}</Text>
              </div>
              <div className="stat-item">
                <Text>{t('terrain.performance.triangles')}</Text>
                <Text>{performanceData.terrainTriangles.toLocaleString()}</Text>
              </div>
              <div className="stat-item">
                <Text>{t('terrain.performance.vertices')}</Text>
                <Text>{performanceData.terrainVertices.toLocaleString()}</Text>
              </div>
              <div className="stat-item">
                <Text>{t('terrain.performance.textureMemory')}</Text>
                <Text>{(performanceData.terrainTextureMemory / (1024 * 1024)).toFixed(1)} MB</Text>
              </div>
              <div className="stat-item">
                <Text>{t('terrain.performance.geometryMemory')}</Text>
                <Text>{(performanceData.terrainGeometryMemory / (1024 * 1024)).toFixed(1)} MB</Text>
              </div>
            </Card>
          </Col>

          <Col span={12}>
            <Card title={t('terrain.performance.bottlenecks')}>
              {bottlenecks.length > 0 ? (
                <div className="bottleneck-list">
                  {bottlenecks.map((bottleneck, index) => (
                    <Tag key={index} color="red" className="bottleneck-tag">
                      <WarningOutlined /> {bottleneck.message}
                    </Tag>
                  ))}
                </div>
              ) : (
                <div className="no-bottlenecks">
                  <CheckCircleOutlined />
                  <Text>{t('terrain.performance.noBottlenecks')}</Text>
                </div>
              )}
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染设置
  const renderSettings = () => {
    return (
      <div className="performance-settings">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.updateFrequency')}</Text>
              <Slider
                min={100}
                max={5000}
                step={100}
                value={updateFrequency}
                onChange={(value) => setUpdateFrequency(value)}
              />
              <Text type="secondary">{updateFrequency} ms</Text>
            </div>
          </Col>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.sampleSize')}</Text>
              <Slider
                min={10}
                max={120}
                step={10}
                value={sampleSize}
                onChange={(value) => setSampleSize(value)}
              />
              <Text type="secondary">{sampleSize}</Text>
            </div>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.recordHistory')}</Text>
              <Switch
                checked={recordHistory}
                onChange={(checked) => setRecordHistory(checked)}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.historyLength')}</Text>
              <Slider
                min={10}
                max={120}
                step={10}
                value={historyLength}
                onChange={(value) => setHistoryLength(value)}
                disabled={!recordHistory}
              />
              <Text type="secondary">{historyLength}</Text>
            </div>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.enableWarnings')}</Text>
              <Switch
                checked={enableWarnings}
                onChange={(checked) => setEnableWarnings(checked)}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.enableBottleneckDetection')}</Text>
              <Switch
                checked={enableBottleneckDetection}
                onChange={(checked) => setEnableBottleneckDetection(checked)}
              />
            </div>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className="terrain-performance-panel">
      <Card
        title={
          <Space>
            <DashboardOutlined />
            <span>{t('terrain.performance.title')}</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              type={isMonitoring ? 'primary' : 'default'}
              icon={isMonitoring ? <PauseOutlined /> : <PlayCircleOutlined />}
              onClick={toggleMonitoring}
            >
              {isMonitoring ? t('terrain.performance.stopMonitoring') : t('terrain.performance.startMonitoring')}
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetPerformanceData}
              disabled={!isMonitoring}
            >
              {t('terrain.performance.reset')}
            </Button>
            <Button
              icon={<SettingOutlined />}
              onClick={() => setShowSettings(!showSettings)}
            >
              {t('terrain.performance.settings')}
            </Button>
          </Space>
        }
      >
        {showSettings ? (
          renderSettings()
        ) : (
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab={t('terrain.performance.overview')} key="overview">
              {renderOverview()}
            </TabPane>
            <TabPane tab={t('terrain.performance.cdlod')} key="cdlod">
              {/* CDLOD性能内容 */}
            </TabPane>
            <TabPane tab={t('terrain.performance.textures')} key="textures">
              {/* 纹理性能内容 */}
            </TabPane>
            <TabPane tab={t('terrain.performance.physics')} key="physics">
              {/* 物理性能内容 */}
            </TabPane>
            <TabPane tab={t('terrain.performance.virtualTexturing')} key="virtualTexturing">
              {/* 虚拟纹理性能内容 */}
            </TabPane>
            <TabPane tab={t('terrain.performance.benchmark')} key="benchmark">
              <TerrainBenchmarkPanel
                entityId={entityId}
                editable={editable}
              />
            </TabPane>
          </Tabs>
        )}
      </Card>
    </div>
  );
};

export default TerrainPerformancePanel;
